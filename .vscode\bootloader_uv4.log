*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'C:\Keil_v5\ARM\ARM_Compiler_5.06u7\Bin'
Rebuild target 'bootloader'
assembling startup_stm32f407xx.s...
compiling stm32f4xx_hal_crc.c...
compiling stm32f4xx_hal_gpio.c...
compiling stm32f4xx_hal_flash_ex.c...
compiling stm32f4xx_hal_rcc_ex.c...
compiling stm32f4xx_hal_flash.c...
compiling stm32f4xx_hal_dma_ex.c...
compiling stm32f4xx_it.c...
compiling usart.c...
compiling stm32f4xx_hal_msp.c...
compiling gpio.c...
compiling stm32f4xx_hal_pwr.c...
compiling stm32f4xx_hal_flash_ramfunc.c...
compiling stm32f4xx_hal_rcc.c...
compiling crc.c...
compiling stm32f4xx_hal_dma.c...
compiling main.c...
compiling ymodem.c...
..\IAP\ymodem.c(69): error:  #20: identifier "UartHandle" is undefined
    status = HAL_UART_Receive(&UartHandle, &char1, 1, timeout);
..\IAP\ymodem.c(316): warning:  #223-D: function "Serial_PutByte" declared implicitly
            Serial_PutByte(ACK);
..\IAP\ymodem.c(342): error:  #20: identifier "aFileName" is undefined
                    aFileName[i++] = *file_ptr++;
..\IAP\ymodem.c(346): error:  #20: identifier "aFileName" is undefined
                  aFileName[i++] = '\0';
..\IAP\ymodem.c(354): warning:  #167-D: argument of type "uint32_t *" is incompatible with parameter of type "int32_t *"
                  Str2Int(file_size, &filesize);
..\IAP\ymodem.c(362): error:  #20: identifier "UartHandle" is undefined
                    HAL_UART_Transmit(&UartHandle, &tmp, 1, NAK_TIMEOUT);
..\IAP\ymodem.c(386): error:  #167: argument of type "uint32_t" is incompatible with parameter of type "volatile uint32_t *"
                if (FLASH_If_Write(flashdestination, (uint32_t *)ramsource, packet_length / 4) == FLASHIF_OK)
..\IAP\ymodem.c(386): error:  #20: identifier "FLASHIF_OK" is undefined
                if (FLASH_If_Write(flashdestination, (uint32_t *)ramsource, packet_length / 4) == FLASHIF_OK)
..\IAP\ymodem.c(406): warning:  #223-D: function "Serial_PutByte" declared implicitly
          Serial_PutByte(CA);
..\IAP\ymodem.c(459): error:  #20: identifier "UartHandle" is undefined
      HAL_UART_Transmit(&UartHandle, &aPacketData[PACKET_START_INDEX], PACKET_SIZE + PACKET_HEADER_SIZE, NAK_TIMEOUT);
..\IAP\ymodem.c(464): warning:  #223-D: function "Serial_PutByte" declared implicitly
      Serial_PutByte(temp_crc >> 8);
..\IAP\ymodem.c(523): error:  #20: identifier "UartHandle" is undefined
        HAL_UART_Transmit(&UartHandle, &aPacketData[PACKET_START_INDEX], pkt_size + PACKET_HEADER_SIZE, NAK_TIMEOUT);
..\IAP\ymodem.c(528): warning:  #223-D: function "Serial_PutByte" declared implicitly
        Serial_PutByte(temp_crc >> 8);
..\IAP\ymodem.c(577): warning:  #223-D: function "Serial_PutByte" declared implicitly
      Serial_PutByte(EOT);
..\IAP\ymodem.c(580): error:  #20: identifier "UartHandle" is undefined
      if (HAL_UART_Receive(&UartHandle, &a_rx_ctrl[0], 1, NAK_TIMEOUT) == HAL_OK)
..\IAP\ymodem.c(620): error:  #20: identifier "UartHandle" is undefined
      HAL_UART_Transmit(&UartHandle, &aPacketData[PACKET_START_INDEX], PACKET_SIZE + PACKET_HEADER_SIZE, NAK_TIMEOUT);
..\IAP\ymodem.c(625): warning:  #223-D: function "Serial_PutByte" declared implicitly
      Serial_PutByte(temp_crc >> 8);
..\IAP\ymodem.c: 7 warnings, 10 errors
compiling dwt_delay.c...
compiling menu.c...
..\IAP\menu.c(68): error:  #167: argument of type "uint8_t *" is incompatible with parameter of type "uint32_t *"
    Size = Ymodem_Receive(&tab_1024[0]);
..\IAP\menu.c: 0 warnings, 1 error
compiling flash_if.c...
compiling common.c...
compiling system_stm32f4xx.c...
compiling stm32f4xx_hal_pwr_ex.c...
compiling stm32f4xx_hal_exti.c...
compiling stm32f4xx_hal.c...
compiling stm32f4xx_hal_cortex.c...
compiling stm32f4xx_hal_uart.c...
"bootloader\bootloader.axf" - 11 Error(s), 7 Warning(s).
Target not created.
Build Time Elapsed:  00:00:18
